{"name": "koishi-plugin-yesimbot-extension-sticker-manager", "description": "YesImBot 表情包管理扩展", "version": "1.0.0", "main": "lib/index.js", "typings": "lib/index.d.ts", "contributors": ["HydroGest <<EMAIL>>"], "homepage": "https://github.com/HydroGest/YesImBot", "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo"}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "workspace:*"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "workspace:*"}, "koishi": {"description": {"zh": "Yes! I'm <PERSON><PERSON>! 表情包偷取和管理功能", "en": "Sticker stealing and management"}, "service": {"required": ["yesimbot"], "implements": ["yesimbot-extension-sticker-manager"]}}, "dependencies": {"sharp": "^0.34.3"}, "keywords": ["koishi", "plugin", "sticker", "emoji", "yesimbot"], "repository": {"type": "git", "url": "git+https://github.com/HydroGest/YesImBot.git", "directory": "packages/sticker-manager"}}