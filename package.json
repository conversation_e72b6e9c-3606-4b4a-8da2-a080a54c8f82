{"name": "@root/yesimbot", "version": "0.0.0", "packageManager": "bun@1.2.0", "private": true, "homepage": "https://github.com/HydroGest/YesImBot", "contributors": ["HydroGest <<EMAIL>>", "Dispure <<EMAIL>>", "Miaowfish <<EMAIL>>", "Touch-Night <<EMAIL>>"], "license": "MIT", "workspaces": ["packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean"}, "devDependencies": {"@types/cheerio": "^1.0.0", "@types/jsdom": "^21.1.7", "@types/mozilla-readability": "^0.2.1", "@types/node": "^22.16.2", "esbuild": "^0.25.6", "tsc-alias": "^1.8.16", "turbo": "2.5.4", "typescript": "^5.8.3", "yml-register": "^1.2.5"}}