# 智能体资源中心与消息系统

## 概览

本方案为 Koishi 智能体（Agent）设计了一个集成的资源管理与消息处理系统。其核心目的是解决在与 LLM 交互时遇到的两大挑战：
1.  **资源瞬时性**：聊天平台中的资源链接（如图片、文件）通常是临时的，直接存入长期记忆或上下文会导致其失效。
2.  **上下文不友好**：Koishi 丰富的消息元素（Element）格式对于 LLM 来说难以直接理解和利用。

本系统通过引入**资源中心 (Asset Service)** 和**消息编解码器 (Message Transformer)**，将平台消息与对 LLM 友好的格式进行双向转换，同时赋予了智能体理解和交互各类资源的能力。

## 1. 资源中心 (Asset Service)

资源中心是整个系统的基石，负责管理所有非临时性资源（图片、音视频、文件）的生命周期。我们将它设计为一个 Koishi 服务，插件可以通过 `ctx.assets` 进行调用。

### 1.1 核心目标

*   **持久化存储**：将来自不同平台的临时资源（如有时效性的下载链接）下载并保存到本地或远程存储中。
*   **统一标识**：为每个持久化的资源分配一个唯一的、稳定的内部 ID（例如 UUID）。
*   **访问抽象**：无论资源存储在何处（本地、S3、OSS等），都提供统一的 API 进行访问。
*   **生命周期管理**：实现资源的自动清理和过期策略，防止空间无限膨胀。
*   **智能体交互**：为 AI Agent 提供查看和理解资源内容的工具。

### 1.2 数据库模型

我们需要一个数据库表来存储资源的元信息。推荐使用 `@koishijs/plugin-database` 提供的模型扩展能力。

**`assets` 表结构:**

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `string` | **主键**，唯一的内部资源 ID (e.g., UUID)。 |
| `type` | `string` | 资源类型，如 `image`, `audio`, `video`, `file`。 |
| `mime` | `string` | 资源的 MIME 类型，如 `image/png`, `application/pdf`。 |
| `hash` | `string` | 文件内容的 SHA256 哈希，用于去重。 |
| `size` | `integer` | 文件大小（字节）。 |
| `url` | `text` | 原始的外部 URL (可选，用于溯源)。 |
| `filename` | `text` | 原始文件名 (可选，用于`<file>`元素)。 |
| `createdAt` | `timestamp` | 创建时间。 |
| `lastUsedAt` | `timestamp` | 最后使用时间（每次发送或访问时更新）。 |

### 1.3 核心 API (`ctx.assets`)

该服务将挂载在 `ctx.assets` 上。

*   **`ctx.assets.create(source, filename?)`**: 创建一个资源。
    *   **source**: `string | Buffer`。可以是 URL、本地文件路径 (`file://...`) 或二进制 Buffer。
    *   **filename**: `string` (可选) 指定文件名。
    *   **返回值**: `Promise<string>` 返回资源的内部 ID。
    *   **内部逻辑**:
        1.  根据 `source` 类型获取资源内容（下载、读取文件）。
        2.  计算文件内容的 `hash`。
        3.  查询数据库中是否存在相同 `hash` 的资源，如果存在，则直接返回其 `id`，并更新 `lastUsedAt`。
        4.  如果不存在，将文件保存到存储系统（本地或远程）。
        5.  在数据库中创建新记录。
        6.  返回新的 `id`。

*   **`ctx.assets.get(id)`**: 获取资源。
    *   **id**: `string` 资源的内部 ID。
    *   **返回值**: `Promise<Buffer>` 返回资源的二进制内容。

*   **`ctx.assets.getURL(id)`**: 获取一个可供外部访问的临时 URL。
    *   **id**: `string` 资源的内部 ID。
    *   **返回值**: `Promise<string>` 返回一个可公开访问的 URL。
    *   **内部逻辑**: Koishi 内部需要通过 `ctx.server` 注册一个 HTTP 端点，如 `https://<your-bot-host>/assets/<id>`。此端点负责从存储系统读取文件并返回。这对于需要公网 URL 的平台适配器至关重要。

*   **`ctx.assets.getInfo(id)`**: 获取资源的元信息。
    *   **id**: `string` 资源的内部 ID。
    *   **返回值**: `Promise<object>` 返回数据库中存储的元信息对象。

### 1.4 资源存储接口 (StorageDriver) <Badge type="primary">可扩展</Badge>

为了支持本地和远程存储，我们采用驱动 (Driver) 模式。

#### 1.4.1 驱动接口

```ts
interface StorageDriver {
  write(id: string, buffer: Buffer): Promise<void>;
  read(id: string): Promise<Buffer>;
  delete(id: string): Promise<void>;
}
```

#### 1.4.2 内置本地存储驱动

```ts
// src/drivers/local.ts
import { promises as fs } from 'fs';
import { resolve } from 'path';
import { Context } from 'koishi';

export class LocalStorageDriver implements StorageDriver {
  private baseDir: string;

  constructor(ctx: Context, config: { path: string }) {
    // 默认存储在 Koishi 数据目录下的 assets 文件夹
    this.baseDir = resolve(ctx.baseDir, config.path || 'data/assets');
    fs.mkdir(this.baseDir, { recursive: true });
  }

  private getPath(id: string) {
    return resolve(this.baseDir, id);
  }

  async write(id: string, buffer: Buffer) {
    await fs.writeFile(this.getPath(id), buffer);
  }

  async read(id: string) {
    return fs.readFile(this.getPath(id));
  }

  async delete(id: string) {
    await fs.unlink(this.getPath(id)).catch(() => {}); // 忽略文件不存在的错误
  }
}
```

#### 1.4.3 配置示例

用户可以在配置文件中选择并配置存储驱动。

```ts
// koishi.config.ts
export default {
  plugins: {
    'assets-service': {
      driver: 'local', // 'local', 's3', 'oss', etc.
      storage: {
        // 对应驱动的配置
        path: 'data/my-assets',
      },
      expire: '30d', // 过期时间
      endpoint: 'https://my-bot.com/assets', // 公开访问端点
    },
  },
}
```

### 1.5 智能体资源交互 (Agent Tools)

这些工具将作为函数提供给 LLM，使其能够与资源中心交互。

*   **`assets.view_file(id: string): Promise<string>`**: 查看文件内容。
    *   **功能**: 这是最重要的工具。它会根据文件类型，以对 LLM 最友好的方式返回内容。
    *   **实现细节**:
        *   **文本文件** (`.txt`, `.md`, `.json`): 直接返回前 4000 个字符的文本内容。
        *   **PDF/Word**: 使用 `pdf-parse`, `mammoth` 等库提取纯文本内容并返回。
        *   **图片**: 若具备多模态能力 (如 GPT-4V)，进行识图并返回图片描述。若无，则返回元信息：“[图片: 文件名 'cat.jpg', 类型 'image/jpeg', 大小 128KB]”。
        *   **压缩包**: 返回 “[压缩包: 文件名 'archive.zip']，请使用 `assets.list_archive` 工具查看其内容。”
        *   **其他二进制文件**: 返回 “[二进制文件: 文件名 'data.bin', 类型 'application/octet-stream']，无法预览内容。”

*   **`assets.list_archive(id: string): Promise<string>`**: 列出压缩包内容。
    *   **功能**: 查看 `.zip`, `.tar` 等压缩文件内的文件列表。
    *   **实现细节**: 使用 `unzipper` 或类似库，返回文件列表及目录结构，如 `["file1.txt", "docs/guide.md"]`。

### 1.6 过期资源清理

*   **机制**: 一个定时任务（例如每天凌晨执行的 `ctx.cron`）。
*   **逻辑**: `DELETE FROM assets WHERE lastUsedAt < NOW() - '30 days'`。同时，从存储系统中调用 `driver.delete(id)` 删除对应的物理文件。
*   **可配置**: 过期时间（例如`30d`）应在插件配置中可调。

## 2. 消息编解码器 (Message Transformer)

这一层负责在 **平台原生消息** 和 **包含内部资源ID的、对LLM友好的消息** 之间进行双向转换。

### 2.1 解码流程 (入 → LLM)

当收到用户的消息时，在将其存入历史记录或喂给 LLM 之前进行转换。

*   **实现**: 使用一个高优先级的中间件和 `h.transformAsync` API。

```ts
import { Context, h } from 'koishi';

export function apply(ctx: Context) {
  const rules: h.AsyncTransformer = {
    // 转换所有资源元素
    async img(attrs) {
      if (!attrs.src || attrs.id) return h('img', attrs); // 如果没有src或已有id，保持原样
      const id = await ctx.assets.create(attrs.src);
      return h('img', { id }); // 返回使用内部ID的新元素，丢弃临时的src
    },
    // ... 对 audio, video, file 进行类似处理
  };

  // 使用高优先级中间件，确保在其他插件（如聊天记录插件）之前执行
  ctx.middleware(async (session, next) => {
    session.elements = await h.transformAsync(session.elements, rules);
    return next();
  }, true);
}
```
*   **结果**:
    *   `你好<img src="https://temp.com/cat.jpg"/>` → `你好<img id="a1b2c3d4"/>`
    *   处理后的消息才会被存入数据库和传递给 LLM。

同时保留一些额外信息，比如 `image.summary`, `file.name` 等。

### 2.2 编码流程 (LLM → 出)

当 LLM 生成回复并准备发送时，需要将内部 ID 转换回平台可识别的格式。

*   **实现**: 对于标准元素，只需要将内部ID转化为外部链接或者 base64 编码的资源数据即可。

*   **结果**: LLM 生成 `好的<img id="e5f6g7h8"/>` → 编码器转换为 `好的<img src="https://my-bot.com/assets/e5f6g7h8"/>` → 用户收到图片。

## 3. 完整工作流程示例

让我们通过一个场景，看看整个系统是如何协同工作的。

1.  **用户发送 PDF 文件**:
    *   用户在 QQ 中向机器人发送了一个名为 `koishi-intro.pdf` 的文件。
    *   QQ 适配器收到消息，生成一个 `Element`：`<file src="https://qq-temp-cdn.com/.../koishi-intro.pdf"/>`。

2.  **解码 (中间件)**:
    *   我们的高优先级中间件捕获到该消息。
    *   `h.transformAsync` 处理 `<file>` 元素。
    *   它调用 `ctx.assets.create("https://qq-temp-cdn.com/...")`。
    *   资源中心下载 PDF，计算哈希，存入存储系统，并在数据库中创建记录，返回 ID `b4c5d6e7`。
    *   中间件将元素替换为 `<file id="b4c5d6e7" name="koishi-intro.pdf"/>`。
    *   此时，传递给后续插件（包括 LLM）的消息是：`你好<file id="b4c5d6e7"/>`。

3.  **LLM 交互**:
    *   用户问：“帮我总结一下这个文件说了什么？”
    *   LLM 在上下文中看到了 `<file id="b4c5d6e7"/>` 和用户的问题。
    *   **思考**: 它决定使用 `assets.view_file` 工具来查看文件内容。
    *   **调用工具**: `assets.view_file(id="b4c5d6e7")`
    *   **系统执行**: `assets.view_file` 函数被调用。它从存储系统中读取 ID 为 `b4c5d6e7` 的文件，使用 `pdf-parse` 提取文本，并将文本作为结果返回给 LLM。

4.  **生成回复**:
    *   LLM 得到了 PDF 的纯文本内容。
    *   它根据文本内容进行总结，生成回复：“这个文件介绍了 Koishi 框架，它是一个功能强大的机器人开发平台...”。

5.  **编码与发送**:
    *   该回复是一个纯文本消息，不包含需要特殊编码的元素。
    *   `bot.sendMessage()` 被调用，消息被正常发送给用户。

通过这个流程，智能体成功地“阅读”并理解了一个平台原生的文件，并与用户进行了有意义的互动，而这一切都无需关心资源的临时性或存储细节。

## LLM 提示词 (Prompt) 指导:

在发给 LLM 的 System Prompt 中，必须明确指导其如何理解和使用这些编码后的消息元素。

> 你是一个聊天机器人。你可以使用以下 XML 标签来丰富你的回复内容：
> * `<quote id="<message_id>"/>`: 回复指定的消息。
> * `<at id="<user_id>"/>`: 提及一个用户。
> * `<image id="<asset_id>"/>`: 发送一张图片。你可以引用在上下文中看到的图片ID。
> * `<sticker category="<category>"/>`: 发送一个指定分类的表情包，例如 cat, dog, funny。